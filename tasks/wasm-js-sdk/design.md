# Design Document

## Overview

This design outlines the architecture for creating a WebAssembly (WASM) version of the SmartSpectra C++ SDK with a JavaScript wrapper. The solution involves isolating the core C++ algorithms, creating abstraction layers for browser-specific functionality, and building a comprehensive JavaScript SDK that provides a developer-friendly API for web applications.

The design follows a three-layer architecture:
1. **WASM Core Layer**: Compiled C++ algorithms for spectral analysis and data processing
2. **JavaScript SDK Layer**: Browser-specific implementations and high-level API
3. **Example Application Layer**: Demonstration of real-world usage patterns

## Architecture

### High-Level Architecture Diagram

```mermaid
graph TB
    subgraph "Browser Environment"
        subgraph "Web Application"
            App[Web Application]
            UI[User Interface]
        end

        subgraph "JavaScript SDK Layer"
            JSSDK[JavaScript SDK]
            VideoCapture[Video Capture Manager]
            NetworkClient[Network Client]
            MemoryManager[Memory Manager]
        end

        subgraph "WASM Layer"
            WASMModule[WASM Module]
            CoreAlgorithms[Core Algorithms]
            AbstractionLayer[Abstraction Interfaces]
        end

        subgraph "Browser APIs"
            GetUserMedia[getUserMedia]
            Canvas[Canvas API]
            Fetch[Fetch API]
            WebGL[WebGL]
        end
    end

    subgraph "External Services"
        PhysiologyAPI[Presage Physiology API]
    end

    App --> JSSDK
    UI --> JSSDK
    JSSDK --> VideoCapture
    JSSDK --> NetworkClient
    JSSDK --> MemoryManager
    JSSDK --> WASMModule

    VideoCapture --> GetUserMedia
    VideoCapture --> Canvas
    NetworkClient --> Fetch
    MemoryManager --> WASMModule

    WASMModule --> CoreAlgorithms
    WASMModule --> AbstractionLayer

    NetworkClient --> PhysiologyAPI
```

### Component Architecture

#### 1. WASM Core Layer

**Core Components:**
- **Container Logic**: Isolated from `cpp/smartspectra/container/` - the main processing pipeline
- **Algorithm Modules**: Core spectral analysis and vital sign detection algorithms
- **Abstraction Interfaces**: Clean interfaces for video input, networking, and data output

**Key Design Decisions:**
- Extract platform-independent business logic from existing Container classes
- Design WASM module as pure computational processor (data-in, data-out)
- Keep all asynchronous operations (networking, video capture) in JavaScript layer
- Use simple result structures to communicate between WASM and JavaScript
- Use Emscripten's embind for C++/JavaScript binding

#### 2. JavaScript SDK Layer

**Core Components:**
- **SmartSpectra Class**: Main SDK interface with simplified, unified API
- **Video Capture Manager**: Handles getUserMedia, canvas operations, and pixel data extraction
- **Network Client**: Manages REST API calls to Presage Physiology service (JavaScript-only)
- **Error Handler**: Provides meaningful error messages and status reporting

**Key Design Decisions:**
- Provide both Promise-based and callback-based APIs for flexibility
- Handle all browser-specific functionality in JavaScript layer
- Implement proper resource cleanup and memory management
- Support both spot and continuous measurement modes

#### 3. Example Application Layer

**Components:**
- **Vanilla JavaScript Example**: Basic HTML/CSS/JS implementation
- **Framework Examples**: Demonstrations with React, Vue, and Angular
- **Build Configuration**: Webpack, Rollup, and Vite configurations

## Type System Alignment

### Cross-Platform Consistency

The WASM JS SDK type system has been aligned with the protobuf definitions used across all SmartSpectra SDKs (C++, Swift, Android) to ensure consistent data structures and API patterns:

**Key Alignment Features:**
- **MetricsBuffer Structure**: Matches protobuf `MetricsBuffer` with `pulse`, `breathing`, `bloodPressure`, `face`, and `metadata` fields
- **Measurement Types**: Implements `Measurement`, `MeasurementWithConfidence`, `DetectionStatus`, and `Strict` interfaces
- **Rich Data Access**: Provides access to traces, confidence levels, stability indicators, and detailed physiological metrics
- **Status Management**: Uses `StatusCode` enum for consistent status reporting across platforms
- **Error Handling**: Implements `ErrorCode` enum for type-safe error management

**Benefits:**
- Developers familiar with native SDKs can easily transition to web implementation
- Consistent data structures enable shared documentation and examples
- Rich physiological data access matches capabilities of native SDKs
- Type safety prevents runtime errors and improves developer experience

### Legacy Type Removal

All legacy compatibility types have been removed to maintain a clean, modern API:
- ~~`VitalSignsResult`~~ → `MetricsBuffer`
- ~~`MeasurementMode`~~ → `OperationMode`
- ~~`ErrorCodes` object~~ → `ErrorCode` enum

## Components and Interfaces

### WASM Module Interface

```cpp
// Enhanced result structures for rich data output
struct ProcessingResult {
    bool has_metrics;
    std::string metrics_json;  // JSON string of MetricsBuffer structure
    std::string status_code;   // StatusCode enum value
    int64_t timestamp;
    bool needs_api_call;       // Indicates if JS should make API call
    double confidence;         // Overall confidence score
    bool success;              // Processing success indicator
    std::string error;         // Error message if success is false
};

// Main WASM interface class - streamlined for pure computation
class SmartSpectraWASM {
public:
    SmartSpectraWASM();

    // Configuration with enhanced settings
    absl::Status Initialize(const std::string& config_json);

    // Core processing with rich output
    ProcessingResult ProcessFrame(const uint8_t* pixel_data, int width, int height, int64_t timestamp);
    ProcessingResult ProcessFrameFromBuffer(const std::string& buffer_data, int width, int height, int64_t timestamp);

    // Mode management
    absl::Status StartMode(const std::string& mode, double duration_seconds = 0);
    absl::Status Stop();

    // API integration
    absl::Status ProcessApiResponse(const std::string& response_json, int64_t original_timestamp);

    // Status and diagnostics
    bool IsInitialized() const;
    bool IsRunning() const;
    std::string GetLastError() const;
    std::string GetCurrentStatus() const;
    std::string GetVersion() const;

    // Memory management
    void Cleanup();
};
```

### JavaScript SDK Interface

```typescript
// Core operation types
type OperationMode = 'spot' | 'continuous';
type QualityLevel = 'good' | 'fair' | 'poor';
type ErrorCategory = 'initialization' | 'configuration' | 'runtime' | 'resource';

// Configuration interface aligned with native SDKs
interface SmartSpectraConfig {
    apiKey?: string;
    mode: OperationMode;
    spotDuration?: number; // 10..120 seconds
    bufferDuration?: number; // 1..30 seconds
    enableEdgeMetrics?: boolean;
    wasmPath?: string;
    debug?: boolean;
    // Video source settings
    cameraPosition?: 'front' | 'back';
    videoConstraints?: MediaTrackConstraints;
    // Processing settings
    scaleInput?: boolean;
    enablePhasicBP?: boolean;
    enableDenseFacemeshPoints?: boolean;
    useFullRangeFaceDetection?: boolean;
    confidenceThreshold?: number; // 0.0..1.0
    qualityThreshold?: number; // 0.0..1.0
}

// Rich metrics data structure aligned with protobuf definitions
interface MetricsBuffer {
    pulse?: Pulse;
    breathing?: Breathing;
    bloodPressure?: BloodPressure;
    face?: Face;
    metadata?: Metadata;
}

interface Pulse {
    rate: MeasurementWithConfidence[];
    trace: Measurement[];
    pulseRespirationQuotient: Measurement[];
    strict?: Strict;
}

interface Breathing {
    rate: MeasurementWithConfidence[];
    upperTrace: Measurement[];
    lowerTrace: Measurement[];
    amplitude: Measurement[];
    apnea: DetectionStatus[];
    respiratoryLineLength: Measurement[];
    baseline: Measurement[];
    inhaleExhaleRatio: Measurement[];
    strict?: Strict;
}

interface Measurement {
    time: number;
    value: number;
    stable: boolean;
}

interface MeasurementWithConfidence extends Measurement {
    confidence: number;
}

interface DetectionStatus {
    time: number;
    detected: boolean;
    stable: boolean;
}

interface Strict {
    value: number;
}

// Status and state management
enum StatusCode {
    PROCESSING_NOT_STARTED = 'PROCESSING_NOT_STARTED',
    PROCESSING_STARTED = 'PROCESSING_STARTED',
    PROCESSING_IN_PROGRESS = 'PROCESSING_IN_PROGRESS',
    PROCESSING_COMPLETED = 'PROCESSING_COMPLETED',
    PROCESSING_FAILED = 'PROCESSING_FAILED',
    INSUFFICIENT_DATA = 'INSUFFICIENT_DATA',
    FACE_NOT_DETECTED = 'FACE_NOT_DETECTED',
    POOR_LIGHTING = 'POOR_LIGHTING',
    EXCESSIVE_MOTION = 'EXCESSIVE_MOTION'
}

interface ModeState {
    mode: OperationMode;
    progress: number; // 0.0..1.0 for spot, -1 for continuous
    frameCount: number;
    duration: number; // elapsed time in seconds
    quality: QualityLevel;
    status: StatusCode;
    isStable: boolean;
}

interface SmartSpectraCallbacks {
    onResult?: (result: MetricsBuffer) => void;
    onStatus?: (status: StatusCode) => void;
    onError?: (error: SmartSpectraError) => void;
    onModeComplete?: (mode: OperationMode, results: MetricsBuffer[]) => void;
}

interface StartOptions {
    mode: OperationMode;
    duration?: number;
    onResult?: (result: MetricsBuffer) => void;
    onStatus?: (status: StatusCode) => void;
    onError?: (error: SmartSpectraError) => void;
}

class SmartSpectra {
    // Static initialization
    static async init(options?: WASMLoadOptions): Promise<SmartSpectra>;
    static getVersion(): string;
    static isSupported(): boolean;
    static getBrowserSupport(): BrowserSupport;
    static unload(): void;

    // Configuration
    configure(config: SmartSpectraConfig): void;

    // Video handling
    async setVideoElement(videoElement: HTMLVideoElement): Promise<void>;
    async setVideoStream(stream: MediaStream): Promise<void>;

    // Processing - unified start method with rich return type
    async start(options: StartOptions): Promise<MetricsBuffer | void>;
    async stop(): Promise<void>;

    // State management
    getCurrentMode(): OperationMode | null;
    getModeProgress(): number;
    getModeState(): ModeState;

    // Utilities
    async requestCameraPermission(): Promise<MediaStream>;
    isInitialized(): boolean;
    getVersion(): string;

    // Error handling
    addErrorListener(listener: (report: ErrorReport) => void): void;
    removeErrorListener(listener: (report: ErrorReport) => void): void;
    getErrorHistory(): ErrorReport[];
    clearErrorHistory(): void;

    // Cleanup
    dispose(): Promise<void>;
}
```

### Video Capture Manager

```typescript
class VideoCaptureManager {
    private canvas: HTMLCanvasElement;
    private context: CanvasRenderingContext2D;
    private videoElement: HTMLVideoElement;

    async initialize(videoElement: HTMLVideoElement): Promise<void>;
    extractPixelData(): Uint8Array;
    getFrameDimensions(): { width: number; height: number };
    getCurrentTimestamp(): number;
    cleanup(): void;
}
```

### Network Client

```typescript
class NetworkClient {
    private apiKey: string;
    private baseUrl: string = 'https://physiology.presagetech.com/api';

    setApiKey(apiKey: string): void;
    async sendMetricsRequest(data: any): Promise<any>;
    async authenticate(): Promise<boolean>;
    handleCorsAndSecurity(): void;
}
```

## Data Models

### Configuration Data Model

```typescript
interface WASMConfig {
    mode: 'spot' | 'continuous';
    spot_duration_s?: number;
    buffer_duration_s?: number;
    enable_edge_metrics: boolean;
    verbosity_level: number;
    // Processing settings
    scale_input?: boolean;
    enable_phasic_bp?: boolean;
    enable_dense_facemesh_points?: boolean;
    use_full_range_face_detection?: boolean;
    confidence_threshold?: number;
    quality_threshold?: number;
}
```

### Metrics Data Model (Aligned with Protobuf Definitions)

```typescript
// Rich metrics structure matching C++, Swift, and Android SDKs
interface MetricsBuffer {
    pulse?: Pulse;
    breathing?: Breathing;
    bloodPressure?: BloodPressure;
    face?: Face;
    metadata?: Metadata;
}

interface Pulse {
    rate: MeasurementWithConfidence[];
    trace: Measurement[];
    pulseRespirationQuotient: Measurement[];
    strict?: Strict; // Final aggregated value
}

interface Breathing {
    rate: MeasurementWithConfidence[];
    upperTrace: Measurement[];
    lowerTrace: Measurement[];
    amplitude: Measurement[];
    apnea: DetectionStatus[];
    respiratoryLineLength: Measurement[];
    baseline: Measurement[];
    inhaleExhaleRatio: Measurement[];
    strict?: Strict; // Final aggregated value
}

interface BloodPressure {
    phasic: MeasurementWithConfidence[];
}

interface Face {
    blinking: DetectionStatus[];
    talking: DetectionStatus[];
    landmarks: Landmarks[];
}

interface Landmarks {
    time: number;
    value: Point2D[];
    stable: boolean;
}

interface Point2D {
    x: number;
    y: number;
}

interface Metadata {
    id: string;
    uploadTimestamp: string;
    apiVersion: string;
    sentAtS: number;
    frameTimestamp: number;
    frameCount: number;
}
```

### Video Frame Data Model

```typescript
interface VideoFrame {
    pixel_data: Uint8Array;
    width: number;
    height: number;
    timestamp: number;
    format: 'RGB' | 'RGBA' | 'BGR';
}
```

## Error Handling

### Error Categories

1. **Initialization Errors**
   - WASM module loading failures
   - Browser compatibility issues
   - Missing required features

2. **Configuration Errors**
   - Invalid API keys
   - Unsupported settings
   - Missing required parameters
   - Invalid threshold values

3. **Runtime Errors**
   - Camera access denied
   - Network connectivity issues
   - Processing failures
   - Insufficient data quality

4. **Resource Errors**
   - Memory allocation failures
   - WASM heap overflow
   - Cleanup failures

### Error Handling Strategy

```typescript
// Comprehensive error class with proper typing
class SmartSpectraError extends Error {
    constructor(
        message: string,
        public code: ErrorCode,
        public category: ErrorCategory,
        public recoverable: boolean = false,
        public cause?: Error
    ) {
        super(message);
        this.name = 'SmartSpectraError';

        // Set the cause if provided (for newer browsers)
        if (cause && 'cause' in Error.prototype) {
            (this as any).cause = cause;
        }
    }
}

// Strongly-typed error codes enum
enum ErrorCode {
    WASM_LOAD_FAILED = 'WASM_LOAD_FAILED',
    CAMERA_ACCESS_DENIED = 'CAMERA_ACCESS_DENIED',
    INVALID_API_KEY = 'INVALID_API_KEY',
    NETWORK_ERROR = 'NETWORK_ERROR',
    PROCESSING_FAILED = 'PROCESSING_FAILED',
    MEMORY_ERROR = 'MEMORY_ERROR',
    INVALID_CONFIG = 'INVALID_CONFIG',
    NOT_INITIALIZED = 'NOT_INITIALIZED',
    ALREADY_RUNNING = 'ALREADY_RUNNING',
    NOT_RUNNING = 'NOT_RUNNING',
    UNSUPPORTED_BROWSER = 'UNSUPPORTED_BROWSER'
}

// Error reporting and recovery
interface ErrorReport {
    error: SmartSpectraError;
    timestamp: number;
    context: Record<string, any>;
    recovery: {
        canRecover: boolean;
        recoveryAction?: string;
        attempts: number;
    };
}

// Browser support detection
interface BrowserSupport {
    webAssembly: boolean;
    getUserMedia: boolean;
    canvas: boolean;
    fetch: boolean;
    webGL: boolean;
    sharedArrayBuffer: boolean;
    overall: boolean;
    missing: string[];
}
```

## Testing Strategy

### Unit Testing

1. **WASM Module Tests**
   - Core algorithm functionality
   - Interface compliance
   - Memory management
   - Error handling

2. **JavaScript SDK Tests**
   - API surface testing
   - Browser compatibility
   - Error scenarios
   - Memory leak detection

3. **Integration Tests**
   - WASM/JavaScript interoperability
   - End-to-end processing pipeline
   - Network communication
   - Video processing accuracy

### Browser Testing

1. **Compatibility Testing**
   - Chrome 80+, Firefox 79+, Safari 14+, Edge 80+
   - Mobile browsers (iOS Safari, Chrome Mobile)
   - Different screen resolutions and camera qualities

2. **Performance Testing**
   - WASM loading time
   - Processing latency
   - Memory usage patterns
   - Frame rate sustainability

3. **Security Testing**
   - CSP compliance
   - CORS handling
   - HTTPS requirements
   - Permission handling

### Testing Tools

- **Jest**: JavaScript unit testing
- **Puppeteer**: Browser automation testing
- **WebDriver**: Cross-browser testing
- **Emscripten Test Suite**: WASM-specific testing

## Implementation Status

### ✅ Completed Phases

#### Phase 1: Proof of Concept ✅
- ✅ Identified and isolated core C++ functions
- ✅ Created basic Emscripten build configuration
- ✅ Implemented minimal JavaScript wrapper
- ✅ Demonstrated C++/JavaScript interoperability

#### Phase 2: Core WASM Module ✅
- ✅ Refactored C++ Container classes to use abstraction interfaces
- ✅ Implemented WASM-compatible versions of core algorithms
- ✅ Created Emscripten bindings using embind
- ✅ Implemented comprehensive error handling and status reporting

#### Phase 3: JavaScript SDK ✅
- ✅ Implemented video capture and pixel data extraction (`VideoCaptureManager`)
- ✅ Created network client for Presage API integration (`NetworkClient`)
- ✅ Built memory management utilities (`MemoryManager`)
- ✅ Implemented both spot and continuous modes (`ModeManager`)
- ✅ Created unified SDK interface (`SmartSpectra` class)

#### Phase 4: Type System Alignment ✅
- ✅ Aligned all types with protobuf definitions from native SDKs
- ✅ Implemented rich `MetricsBuffer` structure
- ✅ Added comprehensive error handling with `ErrorCode` enum
- ✅ Implemented detailed status management with `StatusCode` enum
- ✅ Removed all legacy compatibility types
- ✅ Created centralized type definitions in `types.ts`

### 🚧 Current Phase

#### Phase 5: Integration and Testing 🚧
- ✅ Integrated WASM module with JavaScript SDK
- ✅ Implemented comprehensive error handling system
- 🚧 Create automated test suites
- 🚧 Optimize performance and memory usage
- 🚧 Browser compatibility testing

### 📋 Remaining Phases

#### Phase 6: Examples and Documentation
- 📋 Create vanilla JavaScript example application
- 📋 Build framework-specific examples (React, Vue, Angular)
- ✅ Write comprehensive API documentation
- 📋 Create deployment and build guides
- 📋 Add usage examples and tutorials

#### Phase 7: Distribution and Packaging
- 📋 Set up npm package structure
- 📋 Create CDN-ready bundles
- 📋 Implement semantic versioning
- 📋 Set up automated build and release pipeline
- 📋 Create TypeScript declaration files
- 📋 Set up continuous integration

### Recent Achievements

1. **Complete Type System Overhaul**: Migrated from simple result objects to rich protobuf-aligned data structures
2. **Cross-Platform Consistency**: Achieved type alignment with C++, Swift, and Android SDKs
3. **Enhanced Error Handling**: Implemented comprehensive error reporting and recovery system
4. **Modern API Design**: Removed legacy types and created clean, type-safe interfaces
5. **Rich Data Access**: Enabled access to detailed physiological measurements, traces, and metadata